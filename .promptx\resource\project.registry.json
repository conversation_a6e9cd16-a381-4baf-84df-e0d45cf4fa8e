{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-07-30T13:36:27.607Z", "updatedAt": "2025-07-30T13:36:27.691Z", "resourceCount": 48}, "resources": [{"id": "ai-integration-expert", "source": "project", "protocol": "role", "name": "Ai Integration Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-integration-expert/ai-integration-expert.role.md", "metadata": {"createdAt": "2025-07-30T13:36:27.612Z", "updatedAt": "2025-07-30T13:36:27.612Z", "scannedAt": "2025-07-30T13:36:27.612Z", "path": "role/ai-integration-expert/ai-integration-expert.role.md"}}, {"id": "ai-integration-process", "source": "project", "protocol": "execution", "name": "Ai Integration Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-integration-expert/execution/ai-integration-process.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.615Z", "updatedAt": "2025-07-30T13:36:27.615Z", "scannedAt": "2025-07-30T13:36:27.615Z", "path": "role/ai-integration-expert/execution/ai-integration-process.execution.md"}}, {"id": "ai-integration-thinking", "source": "project", "protocol": "thought", "name": "Ai Integration Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-integration-expert/thought/ai-integration-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.617Z", "updatedAt": "2025-07-30T13:36:27.617Z", "scannedAt": "2025-07-30T13:36:27.617Z", "path": "role/ai-integration-expert/thought/ai-integration-thinking.thought.md"}}, {"id": "ai-novel-architect", "source": "project", "protocol": "role", "name": "Ai Novel Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-architect/ai-novel-architect.role.md", "metadata": {"createdAt": "2025-07-30T13:36:27.619Z", "updatedAt": "2025-07-30T13:36:27.619Z", "scannedAt": "2025-07-30T13:36:27.619Z", "path": "role/ai-novel-architect/ai-novel-architect.role.md"}}, {"id": "ai-novel-development", "source": "project", "protocol": "execution", "name": "Ai Novel Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-architect/execution/ai-novel-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.623Z", "updatedAt": "2025-07-30T13:36:27.623Z", "scannedAt": "2025-07-30T13:36:27.623Z", "path": "role/ai-novel-architect/execution/ai-novel-development.execution.md"}}, {"id": "ai-novel-thinking", "source": "project", "protocol": "thought", "name": "Ai Novel Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-architect/thought/ai-novel-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.625Z", "updatedAt": "2025-07-30T13:36:27.625Z", "scannedAt": "2025-07-30T13:36:27.625Z", "path": "role/ai-novel-architect/thought/ai-novel-thinking.thought.md"}}, {"id": "ai-novel-writer", "source": "project", "protocol": "role", "name": "Ai Novel Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/ai-novel-writer/ai-novel-writer.role.md", "metadata": {"createdAt": "2025-07-30T13:36:27.635Z", "updatedAt": "2025-07-30T13:36:27.635Z", "scannedAt": "2025-07-30T13:36:27.635Z", "path": "role/ai-novel-writer/ai-novel-writer.role.md"}}, {"id": "creative-writing-process", "source": "project", "protocol": "execution", "name": "Creative Writing Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/ai-novel-writer/execution/creative-writing-process.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.637Z", "updatedAt": "2025-07-30T13:36:27.637Z", "scannedAt": "2025-07-30T13:36:27.637Z", "path": "role/ai-novel-writer/execution/creative-writing-process.execution.md"}}, {"id": "creative-writing-thinking", "source": "project", "protocol": "thought", "name": "Creative Writing Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/ai-novel-writer/thought/creative-writing-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.639Z", "updatedAt": "2025-07-30T13:36:27.639Z", "scannedAt": "2025-07-30T13:36:27.639Z", "path": "role/ai-novel-writer/thought/creative-writing-thinking.thought.md"}}, {"id": "data-visualization-expert", "source": "project", "protocol": "role", "name": "Data Visualization Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/data-visualization-expert/data-visualization-expert.role.md", "metadata": {"createdAt": "2025-07-30T13:36:27.640Z", "updatedAt": "2025-07-30T13:36:27.640Z", "scannedAt": "2025-07-30T13:36:27.640Z", "path": "role/data-visualization-expert/data-visualization-expert.role.md"}}, {"id": "visualization-development", "source": "project", "protocol": "execution", "name": "Visualization Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/data-visualization-expert/execution/visualization-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.642Z", "updatedAt": "2025-07-30T13:36:27.642Z", "scannedAt": "2025-07-30T13:36:27.642Z", "path": "role/data-visualization-expert/execution/visualization-development.execution.md"}}, {"id": "data-visualization", "source": "project", "protocol": "thought", "name": "Data Visualization 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/data-visualization-expert/thought/data-visualization.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.643Z", "updatedAt": "2025-07-30T13:36:27.643Z", "scannedAt": "2025-07-30T13:36:27.643Z", "path": "role/data-visualization-expert/thought/data-visualization.thought.md"}}, {"id": "database-architect", "source": "project", "protocol": "role", "name": "Database Architect 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/database-architect/database-architect.role.md", "metadata": {"createdAt": "2025-07-30T13:36:27.644Z", "updatedAt": "2025-07-30T13:36:27.644Z", "scannedAt": "2025-07-30T13:36:27.644Z", "path": "role/database-architect/database-architect.role.md"}}, {"id": "database-development", "source": "project", "protocol": "execution", "name": "Database Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/database-architect/execution/database-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.646Z", "updatedAt": "2025-07-30T13:36:27.646Z", "scannedAt": "2025-07-30T13:36:27.646Z", "path": "role/database-architect/execution/database-development.execution.md"}}, {"id": "database-design", "source": "project", "protocol": "thought", "name": "Database Design 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/database-architect/thought/database-design.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.647Z", "updatedAt": "2025-07-30T13:36:27.647Z", "scannedAt": "2025-07-30T13:36:27.647Z", "path": "role/database-architect/thought/database-design.thought.md"}}, {"id": "<PERSON><PERSON><PERSON>-engineer", "source": "project", "protocol": "role", "name": "Devops Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/devops-engineer/devops-engineer.role.md", "metadata": {"createdAt": "2025-07-30T13:36:27.649Z", "updatedAt": "2025-07-30T13:36:27.649Z", "scannedAt": "2025-07-30T13:36:27.649Z", "path": "role/devops-engineer/devops-engineer.role.md"}}, {"id": "devops-development", "source": "project", "protocol": "execution", "name": "Devops Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/devops-engineer/execution/devops-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.650Z", "updatedAt": "2025-07-30T13:36:27.650Z", "scannedAt": "2025-07-30T13:36:27.650Z", "path": "role/devops-engineer/execution/devops-development.execution.md"}}, {"id": "devops-automation", "source": "project", "protocol": "thought", "name": "Devops Automation 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/devops-engineer/thought/devops-automation.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.652Z", "updatedAt": "2025-07-30T13:36:27.652Z", "scannedAt": "2025-07-30T13:36:27.652Z", "path": "role/devops-engineer/thought/devops-automation.thought.md"}}, {"id": "doc-writer", "source": "project", "protocol": "role", "name": "Doc Writer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/doc-writer/doc-writer.role.md", "metadata": {"createdAt": "2025-07-30T13:36:27.654Z", "updatedAt": "2025-07-30T13:36:27.654Z", "scannedAt": "2025-07-30T13:36:27.654Z", "path": "role/doc-writer/doc-writer.role.md"}}, {"id": "documentation-workflow", "source": "project", "protocol": "execution", "name": "Documentation Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/doc-writer/execution/documentation-workflow.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.656Z", "updatedAt": "2025-07-30T13:36:27.656Z", "scannedAt": "2025-07-30T13:36:27.656Z", "path": "role/doc-writer/execution/documentation-workflow.execution.md"}}, {"id": "documentation-thinking", "source": "project", "protocol": "thought", "name": "Documentation Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/doc-writer/thought/documentation-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.657Z", "updatedAt": "2025-07-30T13:36:27.657Z", "scannedAt": "2025-07-30T13:36:27.657Z", "path": "role/doc-writer/thought/documentation-thinking.thought.md"}}, {"id": "electron-developer", "source": "project", "protocol": "role", "name": "Electron Developer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/electron-developer/electron-developer.role.md", "metadata": {"createdAt": "2025-07-30T13:36:27.658Z", "updatedAt": "2025-07-30T13:36:27.658Z", "scannedAt": "2025-07-30T13:36:27.658Z", "path": "role/electron-developer/electron-developer.role.md"}}, {"id": "electron-development", "source": "project", "protocol": "execution", "name": "Electron Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/electron-developer/execution/electron-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.659Z", "updatedAt": "2025-07-30T13:36:27.659Z", "scannedAt": "2025-07-30T13:36:27.659Z", "path": "role/electron-developer/execution/electron-development.execution.md"}}, {"id": "electron-architecture", "source": "project", "protocol": "thought", "name": "Electron Architecture 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/electron-developer/thought/electron-architecture.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.665Z", "updatedAt": "2025-07-30T13:36:27.665Z", "scannedAt": "2025-07-30T13:36:27.665Z", "path": "role/electron-developer/thought/electron-architecture.thought.md"}}, {"id": "glassmorphism-design-process", "source": "project", "protocol": "execution", "name": "Glassmorphism Design Process 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.667Z", "updatedAt": "2025-07-30T13:36:27.667Z", "scannedAt": "2025-07-30T13:36:27.667Z", "path": "role/glassmorphism-designer/execution/glassmorphism-design-process.execution.md"}}, {"id": "glassmorphism-designer", "source": "project", "protocol": "role", "name": "Glassmorphism Designer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/glassmorphism-designer/glassmorphism-designer.role.md", "metadata": {"createdAt": "2025-07-30T13:36:27.668Z", "updatedAt": "2025-07-30T13:36:27.668Z", "scannedAt": "2025-07-30T13:36:27.668Z", "path": "role/glassmorphism-designer/glassmorphism-designer.role.md"}}, {"id": "design-thinking", "source": "project", "protocol": "thought", "name": "Design Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/glassmorphism-designer/thought/design-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.669Z", "updatedAt": "2025-07-30T13:36:27.669Z", "scannedAt": "2025-07-30T13:36:27.669Z", "path": "role/glassmorphism-designer/thought/design-thinking.thought.md"}}, {"id": "nlp-development", "source": "project", "protocol": "execution", "name": "Nlp Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/nlp-expert/execution/nlp-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.671Z", "updatedAt": "2025-07-30T13:36:27.671Z", "scannedAt": "2025-07-30T13:36:27.671Z", "path": "role/nlp-expert/execution/nlp-development.execution.md"}}, {"id": "nlp-expert", "source": "project", "protocol": "role", "name": "Nlp Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/nlp-expert/nlp-expert.role.md", "metadata": {"createdAt": "2025-07-30T13:36:27.671Z", "updatedAt": "2025-07-30T13:36:27.671Z", "scannedAt": "2025-07-30T13:36:27.671Z", "path": "role/nlp-expert/nlp-expert.role.md"}}, {"id": "nlp-processing", "source": "project", "protocol": "thought", "name": "Nlp Processing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/nlp-expert/thought/nlp-processing.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.672Z", "updatedAt": "2025-07-30T13:36:27.672Z", "scannedAt": "2025-07-30T13:36:27.672Z", "path": "role/nlp-expert/thought/nlp-processing.thought.md"}}, {"id": "performance-development", "source": "project", "protocol": "execution", "name": "Performance Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/performance-expert/execution/performance-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.674Z", "updatedAt": "2025-07-30T13:36:27.674Z", "scannedAt": "2025-07-30T13:36:27.674Z", "path": "role/performance-expert/execution/performance-development.execution.md"}}, {"id": "performance-expert", "source": "project", "protocol": "role", "name": "Performance Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/performance-expert/performance-expert.role.md", "metadata": {"createdAt": "2025-07-30T13:36:27.675Z", "updatedAt": "2025-07-30T13:36:27.675Z", "scannedAt": "2025-07-30T13:36:27.675Z", "path": "role/performance-expert/performance-expert.role.md"}}, {"id": "performance-optimization", "source": "project", "protocol": "thought", "name": "Performance Optimization 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/performance-expert/thought/performance-optimization.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.676Z", "updatedAt": "2025-07-30T13:36:27.676Z", "scannedAt": "2025-07-30T13:36:27.676Z", "path": "role/performance-expert/thought/performance-optimization.thought.md"}}, {"id": "editor-development", "source": "project", "protocol": "execution", "name": "Editor Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/rich-text-editor-expert/execution/editor-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.677Z", "updatedAt": "2025-07-30T13:36:27.677Z", "scannedAt": "2025-07-30T13:36:27.677Z", "path": "role/rich-text-editor-expert/execution/editor-development.execution.md"}}, {"id": "rich-text-editor-expert", "source": "project", "protocol": "role", "name": "Rich Text Editor Expert 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/rich-text-editor-expert/rich-text-editor-expert.role.md", "metadata": {"createdAt": "2025-07-30T13:36:27.678Z", "updatedAt": "2025-07-30T13:36:27.678Z", "scannedAt": "2025-07-30T13:36:27.678Z", "path": "role/rich-text-editor-expert/rich-text-editor-expert.role.md"}}, {"id": "rich-text-editing", "source": "project", "protocol": "thought", "name": "Rich Text Editing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/rich-text-editor-expert/thought/rich-text-editing.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.679Z", "updatedAt": "2025-07-30T13:36:27.679Z", "scannedAt": "2025-07-30T13:36:27.679Z", "path": "role/rich-text-editor-expert/thought/rich-text-editing.thought.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "execution", "name": "Intelligent Routing 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/intelligent-routing.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.680Z", "updatedAt": "2025-07-30T13:36:27.680Z", "scannedAt": "2025-07-30T13:36:27.680Z", "path": "role/system-director/execution/intelligent-routing.execution.md"}}, {"id": "project-management", "source": "project", "protocol": "execution", "name": "Project Management 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/project-management.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.681Z", "updatedAt": "2025-07-30T13:36:27.681Z", "scannedAt": "2025-07-30T13:36:27.681Z", "path": "role/system-director/execution/project-management.execution.md"}}, {"id": "quality-assurance", "source": "project", "protocol": "execution", "name": "Quality Assurance 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/quality-assurance.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.682Z", "updatedAt": "2025-07-30T13:36:27.682Z", "scannedAt": "2025-07-30T13:36:27.682Z", "path": "role/system-director/execution/quality-assurance.execution.md"}}, {"id": "team-coordination", "source": "project", "protocol": "execution", "name": "Team Coordination 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/system-director/execution/team-coordination.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.682Z", "updatedAt": "2025-07-30T13:36:27.682Z", "scannedAt": "2025-07-30T13:36:27.682Z", "path": "role/system-director/execution/team-coordination.execution.md"}}, {"id": "system-director", "source": "project", "protocol": "role", "name": "System Director 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/system-director/system-director.role.md", "metadata": {"createdAt": "2025-07-30T13:36:27.683Z", "updatedAt": "2025-07-30T13:36:27.683Z", "scannedAt": "2025-07-30T13:36:27.683Z", "path": "role/system-director/system-director.role.md"}}, {"id": "intelligent-routing", "source": "project", "protocol": "thought", "name": "Intelligent Routing 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/intelligent-routing.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.684Z", "updatedAt": "2025-07-30T13:36:27.684Z", "scannedAt": "2025-07-30T13:36:27.684Z", "path": "role/system-director/thought/intelligent-routing.thought.md"}}, {"id": "quality-control", "source": "project", "protocol": "thought", "name": "Quality Control 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/quality-control.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.685Z", "updatedAt": "2025-07-30T13:36:27.685Z", "scannedAt": "2025-07-30T13:36:27.685Z", "path": "role/system-director/thought/quality-control.thought.md"}}, {"id": "strategic-thinking", "source": "project", "protocol": "thought", "name": "Strategic Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/strategic-thinking.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.686Z", "updatedAt": "2025-07-30T13:36:27.686Z", "scannedAt": "2025-07-30T13:36:27.686Z", "path": "role/system-director/thought/strategic-thinking.thought.md"}}, {"id": "team-coordination", "source": "project", "protocol": "thought", "name": "Team Coordination 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/system-director/thought/team-coordination.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.686Z", "updatedAt": "2025-07-30T13:36:27.686Z", "scannedAt": "2025-07-30T13:36:27.686Z", "path": "role/system-director/thought/team-coordination.thought.md"}}, {"id": "testing-development", "source": "project", "protocol": "execution", "name": "Testing Development 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/test-engineer/execution/testing-development.execution.md", "metadata": {"createdAt": "2025-07-30T13:36:27.688Z", "updatedAt": "2025-07-30T13:36:27.688Z", "scannedAt": "2025-07-30T13:36:27.688Z", "path": "role/test-engineer/execution/testing-development.execution.md"}}, {"id": "test-engineer", "source": "project", "protocol": "role", "name": "Test Engineer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/test-engineer/test-engineer.role.md", "metadata": {"createdAt": "2025-07-30T13:36:27.689Z", "updatedAt": "2025-07-30T13:36:27.689Z", "scannedAt": "2025-07-30T13:36:27.689Z", "path": "role/test-engineer/test-engineer.role.md"}}, {"id": "testing-strategy", "source": "project", "protocol": "thought", "name": "Testing Strategy 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/test-engineer/thought/testing-strategy.thought.md", "metadata": {"createdAt": "2025-07-30T13:36:27.690Z", "updatedAt": "2025-07-30T13:36:27.690Z", "scannedAt": "2025-07-30T13:36:27.690Z", "path": "role/test-engineer/thought/testing-strategy.thought.md"}}], "stats": {"totalResources": 48, "byProtocol": {"role": 14, "execution": 17, "thought": 17}, "bySource": {"project": 48}}}